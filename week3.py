from numpy import nan
import pandas as pd
data =pd.DataFrame({
 'Name': ['sai', 'sanjay', 'sanju', 'harsha', 'steve', 'lakshith', 'adithya'],
 'Age': [17, 17, 18, 17, 18, 17, 17],
 'Gender': ['M', 'M', 'M', 'M', 'M', 'M', 'M'],
 'Marks': [90, 76, 80, 74, 65, 93, 71],
 'City': ['Hyderabad', 'sklm', 'vizag', nan, 'Hyderabad', nan, 'Hyderabad'],
})
print(data)

data2 = pd.DataFrame({
 'Name': ['sai', 'sanjay', 'sanju', 'harsha', 'steve', 'lakshith', 'adithya'],
 'status': ['pass', 'pass', 'pass', 'pass', 'pass', 'pass', 'pass']})
print("\nDataFrame 2:")
print(data2)
merged_data = pd.merge(data, data2, on='Name')
print("\nMerged DataFrame:")
print(merged_data)

data.replace(nan, 'unknown', inplace=True)
print("\nData after replacing NaN with 'unknown':")
print(data)

print(data.isnull())

print(data.isnull().sum())


from sklearn.preprocessing import MinMaxScaler

fees_status = pd.DataFrame({
'ID': [101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
'PENDING': [1234, 5000, 6000, 7000, 1234, 5000, 2000, 5000, 6000, 8000]
})
scaler = MinMaxScaler()
fees_status['PENDING_NORM'] = scaler.fit_transform(fees_status[['PENDING']])
print(fees_status)
