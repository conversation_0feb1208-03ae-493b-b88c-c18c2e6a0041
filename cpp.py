class Stack:
	def __init__(self):
		self.stack = []

	def push(self, x):
		self.stack.append(x)

	def pop(self):
		if not self.is_empty():
			return self.stack.pop()

	def top(self):
		if not self.is_empty():
			return self.stack[-1]

	def is_empty(self):
		return len(self.stack) == 0

	def display(self):
		print("Current Stack:", self.stack)

print("Case 1:")
s1 = Stack()
s1.push(3)
s1.push(5)
s1.pop()
print("Top element after operations:", s1.top())
s1.display()
print("\n" + "-"*30)

print("Case 2:")

s2 = Stack()
s2.push(3)
s2.push(7)
s2.push(4)
s2.pop()
print("Top element after operations:", s2.top())
s2.display()