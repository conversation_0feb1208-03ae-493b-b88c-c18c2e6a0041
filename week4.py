import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA

# Step 1: Create some 2D data (x, y)
np.random.seed(42)
maths = [7,3,1,3]
science = [4,1,2,3]

# Combine into a 2D array
data = np.column_stack((maths, science))

# Step 2: Apply PCA
pca = PCA(n_components=2)
pca_result = pca.fit_transform(data)

# Step 3: Output the results
print("Original shape:", data.shape)
print("Transformed shape:", pca_result.shape)
print("Explained Variance Ratio:", pca.explained_variance_ratio_)
print("Principal Components:\n", pca.components_)

# Step 4: Plot original data and PCA components
plt.figure(figsize=(8, 6))
plt.scatter(data[:, 0], data[:, 1], alpha=0.6, label='Original Data')

# Plot PCA components
origin = np.mean(data, axis=0)
for length, vector in zip(pca.explained_variance_, pca.components_):
    v = vector * 3 * np.sqrt(length)
    plt.quiver(*origin, *v, angles='xy', scale_units='xy', scale=1, color='red', width=0.01)

plt.title('PCA on (maths, science) Data')
plt.xlabel('maths')
plt.ylabel('science')
plt.legend()
plt.axis('equal')
plt.grid(True)
plt.show()
