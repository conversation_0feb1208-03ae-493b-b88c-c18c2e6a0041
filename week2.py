import pandas as pd
data_set=pd.read_csv("/Users/<USER>/Documents/vscode/python/mlLab/Iris.csv")
data=pd.DataFrame(data_set)
print("Information:")
print(data.info())
numeric_data = data.select_dtypes(include='number')
print("Mean:", numeric_data.mean())
print("Mode:", numeric_data.mode())
print("Median:", numeric_data.median())
print("Variance:", numeric_data.var())
print("Standard deviation:", numeric_data.std())
print("\nQuartiles:")
Q1 = numeric_data.quantile(0.25)
Q2 = numeric_data.quantile(0.50)
Q3 = numeric_data.quantile(0.75)
print("Q1 (25%):\n", Q1)
print("Q2 (50% - Median):\n", Q2)
print("Q3 (75%):\n", Q3)
print("\nInterquartile Range (IQR):")
IQR = Q3 - Q1
print("IQR:", IQR)
lower_bound = Q1 - 1.5 * IQR
upper_bound = Q3 + 1.5 * IQR
print("\nLower Bound:", lower_bound)
print("\nUpper Bound:", upper_bound)